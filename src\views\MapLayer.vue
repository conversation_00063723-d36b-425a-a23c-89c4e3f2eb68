<template>
  <div class="w-full h-full relative">
    <div id="mapContainer" class="w-full h-full z-0"></div>

    <!-- 异常点详情抽屉 -->
    <transition
      enter-active-class="transition-transform duration-300 ease-out"
      leave-active-class="transition-transform duration-300 ease-in"
      enter-from-class="transform translate-x-full"
      enter-to-class="transform translate-x-0"
      leave-from-class="transform translate-x-0"
      leave-to-class="transform translate-x-full"
    >
      <div
        v-if="abnormalStore.state.isAbnormalDetailVisible"
        class="fixed top-6 bottom-6 right-6 w-128 bg-gray-900 shadow-2xl z-50 rounded-xl"
      >
        <div class="h-full flex flex-col">
          <!-- 抽屉头部 -->
          <div
            class="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900 rounded-t-xl"
          >
            <!-- 异常类型标签 -->
            <div class="flex items-center space-x-2">
              <span
                class="px-3 py-1 rounded-full text-sm font-medium"
                :class="
                  getAbnormalTypeClass(
                    abnormalStore.state.abnormalPointDetail.exception_type
                  )
                "
              >
                {{
                  getAbnormalTypeName(
                    abnormalStore.state.abnormalPointDetail.exception_type
                  )
                }}
              </span>
            </div>
            <button
              @click="closeAbnormalDetail"
              class="p-2 hover:bg-gray-700 rounded-lg transition-colors text-white"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            </button>
          </div>

          <!-- 抽屉内容 -->
          <div class="flex-1 overflow-y-auto p-4 scrollbar-hide">
            <div
              v-if="abnormalStore.state.abnormalPointDetail"
              class="space-y-4"
            >
              <!-- 主要信息卡片 -->
              <div
                class="bg-gradient-to-br from-[#09203f] to-[#537895] rounded-xl p-6 text-white shadow-lg"
              >
                <!-- 异常编号和图标 -->
                <div class="flex items-center space-x-3 mb-4">
                  <div
                    class="w-10 h-10 bg-white rounded-full flex items-center justify-center"
                  >
                    <svg
                      class="w-5 h-5 text-gray-900"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold">异常报告</h3>
                    <p class="text-sm text-gray-300">
                      {{
                        abnormalStore.state.abnormalPointDetail?.unusual_code ||
                        "-"
                      }}
                    </p>
                  </div>
                </div>

                <!-- 关键信息网格 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                  <!-- 起始位置 -->
                  <div>
                    <p class="text-xs text-gray-400 mb-1">异常位置</p>
                    <p class="text-sm font-medium">
                      {{
                        abnormalStore.state.abnormalPointDetail
                          ?.exception_location || "-"
                      }}
                    </p>
                  </div>

                  <!-- 目标位置 -->
                  <div>
                    <p class="text-xs text-gray-400 mb-1">车牌号码</p>
                    <p class="text-sm font-medium">
                      {{
                        abnormalStore.state.abnormalPointDetail
                          ?.license_plate || "-"
                      }}
                    </p>
                  </div>

                  <!-- 配送方式 -->
                  <div>
                    <p class="text-xs text-gray-400 mb-1">司机姓名</p>
                    <p class="text-sm font-medium">
                      {{
                        abnormalStore.state.abnormalPointDetail?.driver_name ||
                        "-"
                      }}
                    </p>
                  </div>

                  <!-- 重量 -->
                  <div>
                    <p class="text-xs text-gray-400 mb-1">联系电话</p>
                    <p class="text-sm font-medium">
                      {{
                        abnormalStore.state.abnormalPointDetail?.driver_tel ||
                        "-"
                      }}
                    </p>
                  </div>
                </div>

                <!-- 查看详情按钮 -->
                <button
                  @click="toggleDetailView"
                  class="w-full bg-white/20 hover:bg-white/30 text-white py-3 rounded-lg font-medium transition-colors backdrop-blur-sm"
                >
                  {{ showDetailView ? "收起详情" : "查看详情" }}
                </button>
              </div>

              <!-- 详细信息展开区域 -->
              <div v-if="showDetailView" class="mt-4 space-y-4">
                <!-- 详细信息卡片 -->
                <div
                  class="bg-gradient-to-br from-[#09203f] to-[#537895] rounded-xl p-4 shadow-sm border border-gray-700"
                >
                  <h4 class="text-lg font-semibold text-white mb-4">
                    详细信息
                  </h4>

                  <!-- 时间线样式的详细信息 -->
                  <div class="space-y-4">
                    <!-- 异常时间 -->
                    <div
                      v-if="
                        abnormalStore.state.abnormalPointDetail?.unusual_time
                      "
                      class="flex items-start space-x-3"
                    >
                      <div
                        class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
                      >
                        <svg
                          class="w-4 h-4 text-blue-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h5 class="font-medium text-white">异常时间</h5>
                        <p class="text-sm text-gray-300">
                          {{
                            formatFieldValue(
                              "unusual_time",
                              abnormalStore.state.abnormalPointDetail
                                .unusual_time
                            )
                          }}
                        </p>
                      </div>
                    </div>

                    <!-- 异常原因 -->
                    <div
                      v-if="
                        abnormalStore.state.abnormalPointDetail
                          ?.exception_reason
                      "
                      class="flex items-start space-x-3"
                    >
                      <div
                        class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
                      >
                        <svg
                          class="w-4 h-4 text-orange-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h5 class="font-medium text-white">异常原因</h5>
                        <p class="text-sm text-gray-300">
                          {{
                            abnormalStore.state.abnormalPointDetail
                              .exception_reason
                          }}
                        </p>
                      </div>
                    </div>

                    <!-- 异常备注 -->
                    <div
                      v-if="
                        abnormalStore.state.abnormalPointDetail
                          ?.exception_remarks
                      "
                      class="flex items-start space-x-3"
                    >
                      <div
                        class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
                      >
                        <svg
                          class="w-4 h-4 text-green-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h5 class="font-medium text-white">备注信息</h5>
                        <p class="text-sm text-gray-300">
                          {{
                            abnormalStore.state.abnormalPointDetail
                              .exception_remarks
                          }}
                        </p>
                      </div>
                    </div>

                    <!-- 计划编号 -->
                    <div
                      v-if="abnormalStore.state.abnormalPointDetail?.plan_code"
                      class="flex items-start space-x-3"
                    >
                      <div
                        class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
                      >
                        <svg
                          class="w-4 h-4 text-purple-600"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <h5 class="font-medium text-white">计划编号</h5>
                        <p class="text-sm text-gray-300">
                          {{
                            abnormalStore.state.abnormalPointDetail.plan_code
                          }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 相关图片 -->
                <div
                  v-if="
                    abnormalStore.state.abnormalPointDetail
                      ?.exception_related_photos ||
                    abnormalStore.state.abnormalPointDetail
                      ?.exception_related_photos2
                  "
                  class="bg-gradient-to-br from-[#09203f] to-[#537895] rounded-xl p-4 shadow-sm border border-gray-700"
                >
                  <div class="grid grid-cols-1 gap-4">
                    <!-- 图片1 -->
                    <div
                      v-if="
                        abnormalStore.state.abnormalPointDetail
                          .exception_related_photos
                      "
                      class="space-y-2"
                    >
                      <p class="text-sm font-medium text-white">
                        {{ formatFieldName("exception_related_photos") }}
                      </p>
                      <div class="relative">
                        <img
                          :src="
                            abnormalStore.state.abnormalPointDetail
                              .exception_related_photos
                          "
                          alt="异常相关图片1"
                          class="w-full h-48 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity shadow-sm"
                          @click="
                            previewImage(
                              abnormalStore.state.abnormalPointDetail
                                .exception_related_photos
                            )
                          "
                          @error="handleImageError"
                        />
                      </div>
                    </div>

                    <!-- 图片2 -->
                    <div
                      v-if="
                        abnormalStore.state.abnormalPointDetail
                          .exception_related_photos2
                      "
                      class="space-y-2"
                    >
                      <p class="text-sm font-medium text-white">
                        {{ formatFieldName("exception_related_photos2") }}
                      </p>
                      <div class="relative">
                        <img
                          :src="
                            abnormalStore.state.abnormalPointDetail
                              .exception_related_photos2
                          "
                          alt="异常相关图片2"
                          class="w-full h-48 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity shadow-sm"
                          @click="
                            previewImage(
                              abnormalStore.state.abnormalPointDetail
                                .exception_related_photos2
                            )
                          "
                          @error="handleImageError"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 处理操作区域 -->
              <div
                class="mt-6 bg-gradient-to-br from-[#09203f] to-[#537895] rounded-xl p-4 shadow-sm border border-gray-700"
              >
                <h4
                  class="text-lg font-semibold text-white mb-4 flex items-center"
                >
                  <svg
                    class="w-5 h-5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.532 1.532 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  异常处理
                </h4>

                <!-- 信息输入区域 -->
                <div class="mb-6 space-y-4">
                  <!-- 更换车辆 -->
                  <div class="grid grid-cols-3 gap-4 items-center">
                    <!-- 左列：标签 -->
                    <div class="text-sm font-medium text-white">更换车辆</div>

                    <!-- 中列：回显值 -->
                    <div
                      class="text-sm text-gray-300 bg-gray-700 rounded-lg p-3 min-h-[44px] flex items-center"
                    >
                      <span v-if="selectedVehicle">
                        {{ selectedVehicle.license_plate }}
                      </span>
                      <span v-else class="text-gray-500">未选择</span>
                    </div>

                    <!-- 右列：选择按钮 -->
                    <button
                      @click="openVehicleSelection"
                      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      选择车辆
                    </button>
                  </div>

                  <!-- 更换司机 -->
                  <div class="grid grid-cols-3 gap-4 items-center">
                    <!-- 左列：标签 -->
                    <div class="text-sm font-medium text-white">更换司机</div>

                    <!-- 中列：回显值 -->
                    <div
                      class="text-sm text-gray-300 bg-gray-700 rounded-lg p-3 min-h-[44px] flex items-center"
                    >
                      <span v-if="selectedDriver">
                        {{ selectedDriver.name }}
                      </span>
                      <span v-else class="text-gray-500">未选择</span>
                    </div>

                    <!-- 右列：选择按钮 -->
                    <button
                      @click="openDriverSelection"
                      class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      选择司机
                    </button>
                  </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-white mb-3"
                    >处理证明文件（仅支持单个文件）</label
                  >
                  <div
                    class="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-gray-500 transition-colors"
                    :class="{ 'border-blue-500 bg-blue-50/5': isUploading }"
                  >
                    <input
                      type="file"
                      ref="fileInput"
                      @change="handleFileUpload"
                      class="hidden"
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.7z"
                    />
                    <div
                      @click="$refs.fileInput?.click()"
                      class="cursor-pointer"
                      :class="{ 'pointer-events-none': isUploading }"
                    >
                      <!-- 上传中状态 -->
                      <div v-if="isUploading" class="flex flex-col items-center">
                        <svg
                          class="animate-spin h-12 w-12 text-blue-400 mb-2"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            class="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                          ></circle>
                          <path
                            class="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        <p class="text-sm text-blue-400">正在上传文件...</p>
                      </div>

                      <!-- 正常状态 -->
                      <div v-else>
                        <svg
                          class="mx-auto h-12 w-12 text-gray-400"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          />
                        </svg>
                        <p class="mt-2 text-sm text-gray-300">
                          <span class="font-medium text-blue-400">点击选择文件</span>
                          或拖拽文件到此处
                        </p>
                        <p class="text-xs text-gray-300">
                          仅支持单个文件，格式：文档、表格、压缩包等（不包括图片），最大 10MB
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 已上传文件 -->
                  <div v-if="fileList.length > 0" class="mt-4 space-y-2">
                    <div class="text-xs text-gray-400 mb-2">
                      已上传文件
                    </div>
                    <div
                      v-for="(file, index) in fileList"
                      :key="file.uid || index"
                      class="flex items-center justify-between bg-gray-700 rounded-lg p-3 hover:bg-gray-600 transition-colors"
                    >
                      <div class="flex items-center space-x-3 flex-1 min-w-0">
                        <!-- 文件类型图标 -->
                        <div class="flex-shrink-0">
                          <svg
                            v-if="file.status === 'done'"
                            class="w-5 h-5 text-green-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clip-rule="evenodd"
                            />
                          </svg>
                          <svg
                            v-else
                            class="w-5 h-5 text-blue-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>

                        <!-- 文件信息 -->
                        <div class="flex-1 min-w-0">
                          <div class="text-sm text-white truncate" :title="file.name">
                            {{ file.name }}
                          </div>
                          <div class="text-xs text-gray-400 flex items-center space-x-2">
                            <span>{{ formatFileSize(file.size) }}</span>
                            <span v-if="file.status === 'done'" class="text-green-400">
                              • 上传成功
                            </span>
                            <span v-else-if="file.status === 'uploading'" class="text-blue-400">
                              • 上传中...
                            </span>
                          </div>
                        </div>
                      </div>

                      <!-- 操作按钮 -->
                      <div class="flex items-center space-x-2 flex-shrink-0">
                        <!-- 查看文件链接（如果有URL） -->
                        <button
                          v-if="file.url && file.url !== '#' && file.status === 'done'"
                          @click="viewFile(file.url)"
                          class="text-blue-400 hover:text-blue-300 transition-colors p-1"
                          title="查看文件"
                        >
                          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                          </svg>
                        </button>

                        <!-- 删除按钮 -->
                        <button
                          @click="removeFile()"
                          class="text-red-400 hover:text-red-300 transition-colors p-1"
                          title="删除文件"
                        >
                          <svg
                            class="w-4 h-4"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 异常处理备注 -->
                <div class="mb-6">
                  <label class="block text-sm font-medium text-white mb-3"
                    >异常处理备注</label
                  >
                  <textarea
                    v-model="processRemarks"
                    class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    rows="4"
                    placeholder="请输入异常处理备注信息..."
                    maxlength="500"
                  ></textarea>
                  <div class="text-xs text-gray-400 mt-1 text-right">
                    {{ processRemarks.length }}/500
                  </div>
                </div>

                <!-- 执行按钮 -->
                <div class="pt-4 border-t border-gray-600">
                  <!-- 必填字段提示 -->
                  <div v-if="!canExecute" class="mb-3 p-2 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
                    <div class="text-xs text-yellow-300 flex items-center">
                      <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                      </svg>
                      <span>
                        请完成必填项：
                        <span v-if="!selectedVehicle">选择车辆</span>
                        <span v-if="!selectedVehicle && !selectedDriver"> 和 </span>
                        <span v-if="!selectedDriver">选择司机</span>
                      </span>
                    </div>
                  </div>

                  <button
                    @click="executeProcess"
                    :disabled="!canExecute"
                    :class="[
                      'w-full py-3 rounded-lg font-medium transition-all',
                      canExecute
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg'
                        : 'bg-gray-600 text-gray-400 cursor-not-allowed',
                    ]"
                  >
                    <span
                      v-if="isProcessing"
                      class="flex items-center justify-center"
                    >
                      <svg
                        class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          class="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          stroke-width="4"
                        ></circle>
                        <path
                          class="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      处理中...
                    </span>
                    <span v-else>执行处理</span>
                  </button>
                </div>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-else class="flex items-center justify-center h-32">
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <!-- 图片预览模态框 -->
    <transition
      enter-active-class="transition-opacity duration-300 ease-out"
      leave-active-class="transition-opacity duration-300 ease-in"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="showImagePreview"
        @click="closeImagePreview"
        class="fixed inset-0 bg-black bg-opacity-80 z-[60] flex items-center justify-center p-4"
      >
        <div class="relative max-w-4xl max-h-full">
          <!-- 关闭按钮 -->
          <button
            @click="closeImagePreview"
            class="absolute top-4 right-4 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-all z-10"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>

          <!-- 预览图片 -->
          <img
            :src="previewImageUrl"
            alt="图片预览"
            class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            @click.stop
            @error="handleImageError"
          />
        </div>
      </div>
    </transition>

    <!-- 车辆选择弹窗 -->
    <VehicleSelectionModal
      :visible="showVehicleModal"
      :abnormal-point="abnormalStore.state.abnormalPointDetail"
      :selected-vehicle-data="selectedVehicle"
      :vehicle-data="freeCarData"
      @close="closeVehicleModal"
      @confirm="confirmVehicleSelection"
    />

    <!-- 司机选择弹窗 -->
    <DriverSelectionModal
      :visible="showDriverModal"
      :selected-driver-data="selectedDriver"
      :driver-data="freeDriverData"
      @close="closeDriverModal"
      @confirm="confirmDriverSelection"
    />
  </div>
</template>

<script setup>
import { nextTick, onMounted, watch, ref, onUnmounted, computed } from "vue";
import { getAbnormalPointsApi, getAbnormalDetailApi, getFreeCarApi, getFreeDriverApi, handleAbnormalApi } from "@/api/abnormal";
import { UploadImgApiDirect } from "@/api/upload";
import { useAbnormalStore } from "@/store/index.js";
import VehicleSelectionModal from "@/components/VehicleSelectionModal.vue";
import DriverSelectionModal from "@/components/DriverSelectionModal.vue";
import { message } from "ant-design-vue";
import dayjs from "dayjs";

const abnormalStore = useAbnormalStore();

// 控制详情展开状态
const showDetailView = ref(false);

// 控制图片预览状态
const showImagePreview = ref(false);
const previewImageUrl = ref("");

// 处理操作相关状态
const selectedVehicle = ref(null);
const selectedDriver = ref(null);
const uploadedFiles = ref([]);
const fileList = ref([]);
const processRemarks = ref(''); // 异常处理备注
const isProcessing = ref(false);
const isUploading = ref(false);


// 车辆选择弹窗状态
const showVehicleModal = ref(false);
const freeCarData = ref(null);
const isVehicleDataLoaded = ref(false);

// 司机选择弹窗状态
const showDriverModal = ref(false);
const freeDriverData = ref(null);
const isDriverDataLoaded = ref(false);

let map;
let abnormalMarkers; // 异常点标记实例

// 创建异常点SVG图标
const createAbnormalIcon = (type) => {
  console.log("创建卡车图标，类型:", type);

  const iconConfig = {
    交通事故: { color: "#dc2626" },
    车辆故障: { color: "#ea580c" },
    任务变更: { color: "#2563eb" },
    车辆维修: { color: "#7c3aed" },
    其他: { color: "#6b7280" },
  };

  const config = iconConfig[type] || iconConfig["其他"];

  // 卡车图标版本（32x32像素，地图兼容）
  // 优化的卡车图标，确保在32x32像素下清晰可见
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
    <!-- 卡车图标 (优化缩放和位置) -->
    <g transform="translate(3, 3) scale(1.1)">
      <path fill="${config.color}" d="M3 4c-1.1 0-2 .9-2 2v11h2c0 1.7 1.3 3 3 3s3-1.3 3-3h6c0 1.7 1.3 3 3 3s3-1.3 3-3h2v-5l-3-4h-3V4zm5 2h2v4H8zm9 3.5h2.5l2 2.5H17zM8 12h2v2H8zm-2 3.5c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5s-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5m12 0c.8 0 1.5.7 1.5 1.5s-.7 1.5-1.5 1.5s-1.5-.7-1.5-1.5s.7-1.5 1.5-1.5"/>
    </g>
  </svg>`;

  const dataUrl = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(svg);

  return dataUrl;
};


// 初始化地图
const initMap = () => {
  if (!window.TMap) {
    console.error("腾讯地图 API 未加载");
    return;
  }

  // 创建地图实例
  map = new window.TMap.Map("mapContainer", {
    center: new window.TMap.LatLng(39.90596, 116.341752),
    zoom: 13,
    mapTypeId: "vector",
    baseMap: {
      type: "vector",
      features: ["base", "building3d"],
    },
  });

  // 创建异常点标记实例
  abnormalMarkers = new window.TMap.MultiMarker({
    map: map,
    styles: {
      交通事故: new window.TMap.MarkerStyle({
        width: 32,
        height: 32,
        anchor: { x: 16, y: 32 },
        src: createAbnormalIcon("交通事故"),
        color: "#ffffff",
        strokeColor: "#dc2626",
        strokeWidth: 2,
        size: 12,
        direction: "bottom",
        offset: { x: 0, y: 8 },
        backgroundColor: "#dc2626",
        backgroundBorderRadius: 4,
        padding: "4px 8px",
      }),
      车辆故障: new window.TMap.MarkerStyle({
        width: 32,
        height: 32,
        anchor: { x: 16, y: 32 },
        src: createAbnormalIcon("车辆故障"),
        color: "#ffffff",
        strokeColor: "#ea580c",
        strokeWidth: 2,
        size: 12,
        direction: "bottom",
        offset: { x: 0, y: 8 },
        backgroundColor: "#ea580c",
        backgroundBorderRadius: 4,
        padding: "4px 8px",
      }),
      任务变更: new window.TMap.MarkerStyle({
        width: 32,
        height: 32,
        anchor: { x: 16, y: 32 },
        src: createAbnormalIcon("任务变更"),
        color: "#ffffff",
        strokeColor: "#2563eb",
        strokeWidth: 2,
        size: 12,
        direction: "bottom",
        offset: { x: 0, y: 8 },
        backgroundColor: "#2563eb",
        backgroundBorderRadius: 4,
        padding: "4px 8px",
      }),
      车辆维修: new window.TMap.MarkerStyle({
        width: 32,
        height: 32,
        anchor: { x: 16, y: 32 },
        src: createAbnormalIcon("车辆维修"),
        color: "#ffffff",
        strokeColor: "#7c3aed",
        strokeWidth: 2,
        size: 12,
        direction: "bottom",
        offset: { x: 0, y: 8 },
        backgroundColor: "#7c3aed",
        backgroundBorderRadius: 4,
        padding: "4px 8px",
      }),
      其他: new window.TMap.MarkerStyle({
        width: 32,
        height: 32,
        anchor: { x: 16, y: 32 },
        src: createAbnormalIcon("其他"),
        color: "#ffffff",
        strokeColor: "#6b7280",
        strokeWidth: 2,
        size: 12,
        direction: "bottom",
        offset: { x: 0, y: 8 },
        backgroundColor: "#6b7280",
        backgroundBorderRadius: 4,
        padding: "4px 8px",
      }),
    },
  });

  abnormalMarkers.on("click", handleAbnormalClick);
};

// 验证坐标是否有效
const isValidCoordinate = (latitude, longitude) => {
  const lat = parseFloat(latitude);
  const lng = parseFloat(longitude);

  // 检查是否为有效数字
  if (isNaN(lat) || isNaN(lng)) {
    return false;
  }

  // 检查纬度范围 (-90 到 90)
  if (lat < -90 || lat > 90) {
    return false;
  }

  // 检查经度范围 (-180 到 180)
  if (lng < -180 || lng > 180) {
    return false;
  }

  return true;
};

// 找到第一个有效坐标点
const findFirstValidCoordinate = (abnormalData) => {
  for (let i = 0; i < abnormalData.length; i++) {
    const item = abnormalData[i];
    if (item.position && item.position.lat && item.position.lng) {
      const lat = item.position.lat;
      const lng = item.position.lng;

      if (isValidCoordinate(lat, lng)) {
        return item.position;
      }
    }
  }
  return null;
};

// 设置地图中心到指定坐标
const setMapCenter = (position, zoom = 15) => {
  if (map && position) {
    map.setCenter(position);
    map.setZoom(zoom);
    console.log("地图中心已设置为:", position);
  }
};

// 获取异常点数据
const getAbnormalPoints = async () => {
  try {
    const { result } = await getAbnormalPointsApi();

    const abnormalData = result.map((item) => {
      const [latitude, longitude] =
        item.exception_location_coordinates.split(",");
      return {
        id: item.id,
        position: new window.TMap.LatLng(latitude, longitude),
        type: item.exception_type,
        rawData: item,
      };
    });

    abnormalStore.setAbnormalPoints(abnormalData);

    // 自动定位到第一个有效坐标点
    const firstValidPosition = findFirstValidCoordinate(abnormalData);
    if (firstValidPosition) {
      setMapCenter(firstValidPosition, 15);
    } else {
      console.warn("未找到有效的坐标点，使用默认中心位置");
    }
  } catch (error) {
    console.error("获取异常点失败:", error);
    abnormalStore.setAbnormalPoints([]);
  }
};

// 标记异常点
const markAbnormalPoints = () => {
  if (!abnormalMarkers || !map) return;

  const geometries = abnormalStore.state.abnormalPoints.map((item) => ({
    id: `abnormal_${item.id}`,
    position: item.position,
    content: getAbnormalTypeName(item.type),
    properties: {
      abnormalId: item.id,
      type: item.type,
      isAbnormal: true,
    },
    styleId: item.type,
  }));

  abnormalMarkers.setGeometries(geometries);
};

// 处理异常点点击
const handleAbnormalClick = async (e) => {
  const abnormalId = e.geometry.properties.abnormalId;
  if (!abnormalId) return;

  try {
    abnormalStore.setAbnormalDetailVisible(true);
    abnormalStore.setAbnormalPointDetail(null);

    const { result } = await getAbnormalDetailApi(abnormalId);
    abnormalStore.setAbnormalPointDetail(result[0]);
  } catch (error) {
    console.error("获取异常点详情失败:", error);
  }
};

// 关闭异常点详情
const closeAbnormalDetail = () => {
  abnormalStore.setAbnormalDetailVisible(false);
  abnormalStore.setAbnormalPointDetail(null);
  showDetailView.value = false; // 重置详情展开状态
  closeImagePreview(); // 关闭图片预览
  resetProcessForm(); // 重置处理表单
};

// 切换详情视图
const toggleDetailView = () => {
  showDetailView.value = !showDetailView.value;
};

// 获取异常类型名称
const getAbnormalTypeName = (type) => {
  const typeMap = {
    交通事故: "交通事故",
    车辆故障: "车辆故障",
    任务变更: "任务变更",
    车辆维修: "车辆维修",
    其他: "其他异常",
  };
  return typeMap[type] || "未知异常";
};

// 获取异常类型样式类
const getAbnormalTypeClass = (type) => {
  const classMap = {
    交通事故: "bg-red-100 text-red-800",
    车辆故障: "bg-orange-100 text-orange-800",
    任务变更: "bg-blue-100 text-blue-800",
    车辆维修: "bg-purple-100 text-purple-800",
    其他: "bg-gray-100 text-gray-800",
  };
  return classMap[type] || "bg-gray-100 text-gray-800";
};

// 格式化字段名
const formatFieldName = (key) => {
  const fieldMap = {
    unusual_code: "异常编号",
    plan_code: "计划编号",
    license_plate: "车牌号码",
    driver_name: "司机姓名",
    driver_tel: "司机电话",
    exception_type: "异常类型",
    exception_location: "异常位置",
    exception_remarks: "异常备注",
    exception_related_photos: "相关图片1",
    exception_related_photos2: "相关图片2",
    exception_reason: "异常原因",
    unusual_time: "异常时间",
  };
  return fieldMap[key] || key;
};

// 格式化字段值
const formatFieldValue = (key, value) => {
  if (value === null || value === undefined || value === "") return "-";

  // 时间字段特殊处理
  if (key === "unusual_time") {
    try {
      const date = dayjs(value).subtract(8, "hours");
      if (date.isValid()) {
        return date.format("YYYY-MM-DD HH:mm:ss");
      }
      return "-";
    } catch (error) {
      console.error("时间格式化错误:", error);
      return "-";
    }
  }

  if (typeof value === "object") return JSON.stringify(value);
  return String(value);
};

// 图片预览功能
const previewImage = (imageUrl) => {
  if (imageUrl) {
    previewImageUrl.value = imageUrl;
    showImagePreview.value = true;
  }
};

// 关闭图片预览
const closeImagePreview = () => {
  showImagePreview.value = false;
  previewImageUrl.value = "";
};

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === "Escape" && showImagePreview.value) {
    closeImagePreview();
  }
};

// 重置处理表单
const resetProcessForm = () => {
  selectedVehicle.value = null;
  selectedDriver.value = null;
  uploadedFiles.value = [];
  fileList.value = [];
  processRemarks.value = '';
  isProcessing.value = false;
  isUploading.value = false;
  showVehicleModal.value = false;
  showDriverModal.value = false;
};

// 打开车辆选择模态框
const openVehicleSelection = async () => {
  // 如果还没有加载过车辆数据，则首次加载
  if (!isVehicleDataLoaded.value) {
    try {
      const response = await getFreeCarApi();
      freeCarData.value = response;
      isVehicleDataLoaded.value = true;
    } catch (error) {
      console.error('获取车辆数据失败:', error);
      isVehicleDataLoaded.value = true;
    }
  }

  showVehicleModal.value = true;
};

// 关闭车辆选择模态框
const closeVehicleModal = () => {
  showVehicleModal.value = false;
};

// 确认车辆选择
const confirmVehicleSelection = ({ vehicle }) => {
  selectedVehicle.value = {
    id: vehicle.id, // 使用id作为车辆ID
    license_plate: vehicle.license_plate,
    rated_load_kg: vehicle.rated_load_kg,
    vehicle_volume: vehicle.vehicle_volume,
    driver_name: vehicle.driver_name,
    driver_code: vehicle.driver_code,
    driver_tel: vehicle.driver_tel
  };

  // 如果车辆有司机信息，自动填写司机字段
  if (vehicle.driver_name) {
    selectedDriver.value = {
      id: vehicle.driver_id, // 使用driver_id作为司机ID
      name: vehicle.driver_name,
      code: vehicle.driver_code,
      phone: vehicle.driver_tel || '-'
    };
  }

  showVehicleModal.value = false;
};

// 打开司机选择模态框
const openDriverSelection = async () => {
  // 如果还没有加载过司机数据，则首次加载
  if (!isDriverDataLoaded.value) {
    try {
      const response = await getFreeDriverApi();
      freeDriverData.value = response;
      isDriverDataLoaded.value = true;
    } catch (error) {
      console.error('获取司机数据失败:', error);
      // 使用模拟数据作为备用
      isDriverDataLoaded.value = true;
    }
  }

  showDriverModal.value = true;
};

// 关闭司机选择模态框
const closeDriverModal = () => {
  showDriverModal.value = false;
};

// 确认司机选择
const confirmDriverSelection = ({ driver }) => {
  selectedDriver.value = {
    id: driver.id,
    name: driver.driver_name,
    code: driver.driver_code
  };

  showDriverModal.value = false;
};

// 文件类型验证
const validateFileType = (file) => {
  // 排除图片类型，支持其他常见文件类型
  const imageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];

  if (imageTypes.includes(file.type)) {
    message.error(`不支持上传图片文件：${file.name}`);
    return false;
  }

  // 支持的文件类型（非图片）
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed'
  ];

  // 如果文件类型不在允许列表中，但也不是图片，则允许上传（兼容其他文件类型）
  return true;
};

// 自定义上传函数
const customRequest = async (file) => {
  if (!validateFileType(file)) {
    return;
  }

  console.log('上传文件:', file);

  const fileParams = new FormData();
  fileParams.append('file', file);
  fileParams.append('file_rel_id', 'id_test');
  fileParams.append('file_rel_Object', 'object_test');
  fileParams.append('file_max_num', '1'); // 确保是字符串格式

  try {
    isUploading.value = true;

    console.log('FormData 内容:', {
      file: file.name,
      file_rel_id: fileParams.get('file_rel_id'),
      file_rel_Object: fileParams.get('file_rel_Object'),
      file_max_num: fileParams.get('file_max_num')
    });

    console.log('上传文件到:', '/basic-api/file/upload');
    const response = await UploadImgApiDirect(fileParams);
    console.log('上传响应:', response);

    // 更新文件列表（单文件模式，替换现有文件）
    const fileUrl = response.data?.data?.url || response.data?.url || response.url || null;
    const uploadedFile = {
      url: fileUrl,
      name: file.name,
      status: 'done',
      size: file.size,
      uid: Date.now() + Math.random()
    };

    // 清空现有文件，只保留新上传的文件
    fileList.value = [uploadedFile];
    uploadedFiles.value = [file];
    console.log('更新后的文件列表:', fileList.value);
    
    message.success('上传成功');
  } catch (error) {
    console.error('文件上传失败:', error);
    console.error('错误详情:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      headers: error.config?.headers
    });

    // 简化错误提示
    const errorMessage = `文件 ${file.name} 上传失败: ${error.response?.data?.message || error.message}`;
    message.error(errorMessage);
  } finally {
    isUploading.value = false;
  }
};

// 文件上传处理（单文件模式）
const handleFileUpload = async (event) => {
  const files = Array.from(event.target.files);

  // 只处理第一个文件（单文件模式）
  if (files.length > 0) {
    const file = files[0];

    // 检查文件大小
    if (file.size > 10 * 1024 * 1024) {
      message.error(`文件 ${file.name} 超过10MB限制`);
      event.target.value = "";
      return;
    }

    // 执行上传
    await customRequest(file);
  }

  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

// 移除文件（单文件模式，直接清空）
const removeFile = () => {
  uploadedFiles.value = [];
  fileList.value = [];
  message.success('文件已移除');
};

// 查看文件
const viewFile = (url) => {
  if (url && url !== '#') {
    try {
      window.open(url, '_blank');
    } catch (error) {
      console.error('打开文件失败:', error);
      message.error('无法打开文件');
    }
  } else {
    message.error('文件链接无效');
  }
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 检查是否可以执行处理
const canExecute = computed(() => {
  // 必填字段：选择车辆和司机
  return selectedVehicle.value && selectedDriver.value;
});

// 执行处理
const executeProcess = async () => {
  // 验证必填字段
  if (!selectedVehicle.value) {
    message.error('请选择更换车辆');
    return;
  }

  if (!selectedDriver.value) {
    message.error('请选择更换司机');
    return;
  }

  if (isProcessing.value) return;

  isProcessing.value = true;

  try {
    // 构建API调用参数
    const params = {
      id: abnormalStore.state.abnormalPointDetail?.id,
      car_id: selectedVehicle.value.id,
      driver_id: selectedDriver.value.id,
      file_url: fileList.value.length > 0 ? fileList.value[0].url : null,
      remarks: processRemarks.value.trim() || null
    };

    console.log('调用异常处理API，参数:', params);

    // 调用异常处理API
    // const response = await handleAbnormalApi(params);
    // console.log('异常处理API响应:', response);


    // 处理成功
    message.success(`异常处理成功！`);

    // 重置表单并关闭抽屉
    resetProcessForm();

    // 刷新异常点数据
    markAbnormalPoints();

  } catch (error) {
    console.error("异常处理失败:", error);
    const errorMessage = error.response?.data?.message || error.message || "处理失败，请重试";
    message.error(`异常处理失败: ${errorMessage}`);
  } finally {
    isProcessing.value = false;
  }
};

// 图片加载错误处理
const handleImageError = (event) => {
  console.error("图片加载失败:", event.target.src);
  event.target.style.display = "none";
  // 可以在这里添加默认图片或错误提示
};

nextTick(async () => {
  // 初始化地图
  initMap();

  // 简化初始化流程，直接在地图创建后执行
  setTimeout(async () => {
    console.log("开始获取异常点数据...");
    await getAbnormalPoints();
    console.log("异常点数据获取完成，开始标记异常点...");
    markAbnormalPoints();
  }, 1000); // 等待1秒确保地图完全初始化
});

onMounted(() => {
  // 添加键盘事件监听
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener("keydown", handleKeydown);
});

// 监听异常点数据变化
watch(
  () => abnormalStore.state.abnormalPoints,
  (newPoints) => {
    if (abnormalMarkers && map) {
      markAbnormalPoints();

      // 如果有新的异常点数据，自动定位到第一个有效坐标
      if (newPoints && newPoints.length > 0) {
        const firstValidPosition = findFirstValidCoordinate(newPoints);
        if (firstValidPosition) {
          setMapCenter(firstValidPosition, 15);
        }
      }
    }
  }
);
</script>

<style scoped>
/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  /* Firefox */
  scrollbar-width: none;
  /* Safari and Chrome */
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
