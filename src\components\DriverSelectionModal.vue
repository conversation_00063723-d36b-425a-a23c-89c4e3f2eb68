<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-opacity-50 z-[70] flex items-center justify-center p-4"
    @click="handleBackdropClick"
  >
    <div
      class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[80vh] flex flex-col"
      @click.stop
    >
      <!-- 弹窗头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900">选择司机</h3>
        <button
          @click="handleClose"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- 搜索栏 -->
      <div class="p-6 border-b border-gray-200">
        <div class="relative">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索司机姓名、司机编号..."
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <svg
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <!-- 司机列表 -->
      <div class="flex-1 overflow-y-auto p-6">
        <div v-if="loading" class="flex items-center justify-center h-32">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>

        <div v-else-if="filteredDrivers.length === 0" class="text-center py-12">
          <svg
            class="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            />
          </svg>
          <p class="mt-2 text-sm text-gray-500">暂无司机数据</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="driver in paginatedDrivers"
            :key="driver.id"
            class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
            :class="{ 'border-blue-500 bg-blue-50': selectedDriver?.id === driver.id }"
            @click="selectDriver(driver)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-4 mb-3">
                  <div class="flex items-center space-x-2">
                    <span class="text-lg font-semibold text-gray-900">
                      {{ driver.driver_name }}
                    </span>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      已关联
                    </span>
                  </div>
                </div>

                <div class="text-sm">
                  <span class="text-gray-500">司机编号:</span>
                  <span class="ml-1 font-medium">{{ driver.driver_code || '-' }}</span>
                </div>
              </div>

              <!-- 链接图标 -->
              <div class="ml-4 flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg
                    class="w-4 h-4 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            共 {{ filteredDrivers.length }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
          </div>
          <div class="flex space-x-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            <button
              @click="currentPage++"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
        <button
          @click="handleClose"
          class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="!selectedDriver"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          确认选择
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectedDriverData: {
    type: Object,
    default: null
  },
  driverData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'confirm'])

// 响应式数据
const loading = ref(false)
const drivers = ref([])
const selectedDriver = ref(null)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const filteredDrivers = computed(() => {
  if (!searchKeyword.value) return drivers.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return drivers.value.filter(driver => 
    driver.driver_name?.toLowerCase().includes(keyword) ||
    driver.driver_code?.toLowerCase().includes(keyword)
  )
})

const totalPages = computed(() => {
  return Math.ceil(filteredDrivers.value.length / pageSize.value)
})

const paginatedDrivers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDrivers.value.slice(start, end)
})

// 方法
const handleBackdropClick = (e) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}

const handleClose = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  emit('close')
}

const selectDriver = (driver) => {
  selectedDriver.value = driver
}

const handleConfirm = () => {
  if (!selectedDriver.value) return
  
  emit('confirm', {
    driver: selectedDriver.value
  })
  handleClose()
}

// 获取司机数据
const fetchDriverData = () => {
  if (props.driverData) {
    drivers.value = props.driverData.result || []
  } else {
    drivers.value = []
  }
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchDriverData()
    // 恢复之前选中的司机
    if (props.selectedDriverData) {
      selectedDriver.value = props.selectedDriverData
    }
  }
})

// 监听传入的选中司机数据变化
watch(() => props.selectedDriverData, (newVal) => {
  selectedDriver.value = newVal
})

// 重置搜索时回到第一页
watch(searchKeyword, () => {
  currentPage.value = 1
})

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// 清理键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
