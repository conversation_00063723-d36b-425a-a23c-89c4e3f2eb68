<template>
  <div
    v-if="visible"
    class="fixed inset-0 bg-opacity-50 z-[70] flex items-center justify-center p-4"
    @click="handleBackdropClick"
  >
    <div
      class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[80vh] flex flex-col"
      @click.stop
    >
      <!-- 弹窗头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900">选择车辆</h3>
        <button
          @click="handleClose"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg
            class="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      <!-- 搜索栏 -->
      <div class="p-6 border-b border-gray-200">
        <div class="relative">
          <input
            v-model="searchKeyword"
            type="text"
            placeholder="搜索车牌号、司机姓名..."
            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <svg
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <!-- 车辆列表 -->
      <div class="flex-1 overflow-y-auto p-6">
        <div v-if="loading" class="flex items-center justify-center h-32">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>

        <div v-else-if="filteredVehicles.length === 0" class="text-center py-12">
          <svg
            class="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p class="mt-2 text-sm text-gray-500">暂无车辆数据</p>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="vehicle in paginatedVehicles"
            :key="vehicle.device_code"
            class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
            :class="{ 'border-blue-500 bg-blue-50': selectedVehicle?.device_code === vehicle.device_code }"
            @click="selectVehicle(vehicle)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-4 mb-3">
                  <div class="flex items-center space-x-2">
                    <span class="text-lg font-semibold text-gray-900">
                      {{ vehicle.license_plate }}
                    </span>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      已关联
                    </span>
                  </div>
                  <div class="text-sm text-gray-500">
                    距离: {{ vehicle.distance ? vehicle.distance + 'km' : '未知' }}
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-gray-500">载重:</span>
                    <span class="ml-1 font-medium">{{ vehicle.rated_load_kg || '-' }}kg</span>
                  </div>
                  <div>
                    <span class="text-gray-500">容积:</span>
                    <span class="ml-1 font-medium">{{ vehicle.vehicle_volume || '-' }}m³</span>
                  </div>
                  <div>
                    <span class="text-gray-500">司机:</span>
                    <span class="ml-1 font-medium">{{ vehicle.driver_name || '未分配' }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">司机编号:</span>
                    <span class="ml-1 font-medium">{{ vehicle.driver_code || '-' }}</span>
                  </div>
                </div>
              </div>

              <!-- 链接图标 -->
              <div class="ml-4 flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg
                    class="w-4 h-4 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            共 {{ filteredVehicles.length }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
          </div>
          <div class="flex space-x-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            <button
              @click="currentPage++"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
        <button
          @click="handleClose"
          class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="!selectedVehicle"
          class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          确认选择
        </button>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  abnormalPoint: {
    type: Object,
    default: null
  },
  selectedVehicleData: {
    type: Object,
    default: null
  },
  vehicleData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'confirm'])

// 响应式数据
const loading = ref(false)
const vehicles = ref([])
const selectedVehicle = ref(null)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const filteredVehicles = computed(() => {
  if (!searchKeyword.value) return vehicles.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return vehicles.value.filter(vehicle => 
    vehicle.license_plate?.toLowerCase().includes(keyword) ||
    vehicle.driver_name?.toLowerCase().includes(keyword) ||
    vehicle.driver_code?.toLowerCase().includes(keyword)
  )
})

const totalPages = computed(() => {
  return Math.ceil(filteredVehicles.value.length / pageSize.value)
})

const paginatedVehicles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredVehicles.value.slice(start, end)
})

// 方法
const handleBackdropClick = (e) => {
  if (e.target === e.currentTarget) {
    handleClose()
  }
}

const handleClose = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  emit('close')
}

const selectVehicle = (vehicle) => {
  selectedVehicle.value = vehicle
}

const handleConfirm = () => {
  if (!selectedVehicle.value) return

  emit('confirm', {
    vehicle: selectedVehicle.value
  })
  handleClose()
}

// 计算距离的函数
const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371 // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return (R * c).toFixed(2)
}

// 处理车辆数据
const processVehicleData = (freeCarData) => {
  if (!freeCarData?.result || !Array.isArray(freeCarData.result) || freeCarData.result.length < 2) {
    return []
  }

  const [vehicleList, locationList] = freeCarData.result
  const processedVehicles = []

  vehicleList.forEach(vehicle => {
    // 根据device_code匹配位置信息
    const location = locationList.find(loc => loc.imei === vehicle.device_code)

    if (location) {
      let distance = 0
      console.log('location',props.abnormalPoint);
      
      // 如果有异常点坐标，计算距离
      if (props.abnormalPoint?.exception_location_coordinates) {
        try {
          const [abnormalLat, abnormalLng] = props.abnormalPoint.exception_location_coordinates.split(',').map(Number)
          distance = calculateDistance(abnormalLat, abnormalLng, location.lat_tx, location.lng_tx)
        } catch (error) {
          console.error('计算距离失败:', error)
          distance = 0
        }
      }

      processedVehicles.push({
        ...vehicle,
        lat_tx: location.lat_tx,
        lng_tx: location.lng_tx,
        distance: parseFloat(distance)
      })
    }
  })

  // 按距离排序
  return processedVehicles.sort((a, b) => a.distance - b.distance)
}

// 获取车辆数据
const fetchVehicleData = () => {
  if (props.vehicleData) {
    vehicles.value = processVehicleData(props.vehicleData)
  } else {
    vehicles.value = []
  }
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchVehicleData()
    // 恢复之前选中的车辆
    if (props.selectedVehicleData) {
      selectedVehicle.value = props.selectedVehicleData
    }
  }
})

// 监听传入的选中车辆数据变化
watch(() => props.selectedVehicleData, (newVal) => {
  selectedVehicle.value = newVal
})

// 重置搜索时回到第一页
watch(searchKeyword, () => {
  currentPage.value = 1
})

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

// 清理键盘事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>
