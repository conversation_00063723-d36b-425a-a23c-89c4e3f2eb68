import { http } from "@/utils/http";

/**
 * 获取所有的异常点地理坐标信息
 */
export const getAbnormalPointsApi = () => {
  return http.get("/abnormal/location_point");
};

/**
 * 获取异常点详情
 */
export const getAbnormalDetailApi = (id) => {
  return http.post(`/abnormal/information`, {
    id: id,
  });
};

/**
 * 获取空闲车辆定位
 */
export const getFreeCarApi = () => {
  return http.get("/abnormal/lately_car");
};

/**
 * 获取空闲司机列表
 */
export const getFreeDriverApi = () => {
  return http.get("/abnormal/free_driver");
};