import { http } from "@/utils/http";
import axios from "axios";

export async function UploadImgApi(data) {
  return http.post("/file/upload", data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 直接上传到服务器的备用方案
export async function UploadImgApiDirect(data) {
  return axios.post("https://tms2dev.xcspzg.com/basic-api/file/upload", data, {
    headers: {
      "Content-Type": "multipart/form-data",
      "Authorization": "Bearer 1", // 使用相同的认证
    },
    timeout: 30000, // 30秒超时
  });
}