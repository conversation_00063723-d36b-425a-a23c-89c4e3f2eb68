import { defineStore } from "pinia";
import { ref, computed, reactive } from "vue";

export const useAbnormalStore = defineStore("abnormal", () => {
  const state = reactive({
    // 异常点相关状态
    abnormalPoints: [], // 所有异常点数据
    selectedAbnormalPoint: null, // 选中的异常点
    isAbnormalDetailVisible: false, // 异常点详情抽屉是否显示
    abnormalPointDetail: null, // 异常点详细信息
  });

  // 设置异常点数据
  const setAbnormalPoints = (points) => {
    state.abnormalPoints = points;
  };

  // 设置选中的异常点
  const setSelectedAbnormalPoint = (point) => {
    state.selectedAbnormalPoint = point;
  };

  // 显示/隐藏异常点详情抽屉
  const setAbnormalDetailVisible = (visible) => {
    state.isAbnormalDetailVisible = visible;
  };

  // 设置异常点详细信息
  const setAbnormalPointDetail = (detail) => {
    state.abnormalPointDetail = detail;
  };

  return {
    state,
    setAbnormalPoints,
    setSelectedAbnormalPoint,
    setAbnormalDetailVisible,
    setAbnormalPointDetail,
  };
});
