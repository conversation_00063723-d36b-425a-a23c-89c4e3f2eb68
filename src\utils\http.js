// axios请求封装
import axios from "axios";

// 创建axios实例
const service = axios.create({
  baseURL: "/basic-api", // 根据环境设置基础 URL
  timeout: 10000, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
    Authorization: "Bearer 1",
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 可以在这里添加token
    const token = localStorage.getItem("accessToken");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    // 对请求错误做些什么
    console.error("请求错误:", error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    const res = response.data;

    // 根据实际后端接口调整状态码判断
    if (res.code !== undefined && res.code !== 200) {
      console.error("接口返回错误:", res.message || "未知错误");
      return Promise.reject(new Error(res.message || "请求失败"));
    }

    return res;
  },
  (error) => {
    // 对响应错误做点什么
    console.error("响应错误:", error);

    let message = "网络请求失败";

    if (error.response) {
      // 请求已发出，但服务器响应的状态码不在 2xx 范围内
      switch (error.response.status) {
        case 400:
          message = "请求参数错误";
          break;
        case 401:
          message = "未授权，请重新登录";
          // 可以在这里处理登录失效
          localStorage.removeItem("token");
          break;
        case 403:
          message = "拒绝访问";
          break;
        case 404:
          message = "请求资源不存在";
          break;
        case 500:
          message = "服务器内部错误";
          break;
        default:
          message = `请求失败，状态码：${error.response.status}`;
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      message = "网络连接超时";
    }

    // 显示错误信息（可以根据需要替换为其他通知方式）
    console.error(message);

    return Promise.reject(error);
  }
);

// 封装常用的请求方法
export const http = {
  get(url, params) {
    return service({
      method: "get",
      url,
      params,
    });
  },

  post(url, data) {
    return service({
      method: "post",
      url,
      data,
    });
  },

  put(url, data) {
    return service({
      method: "put",
      url,
      data,
    });
  },

  delete(url, data) {
    return service({
      method: "delete",
      url,
      data,
    });
  },
};

export default service;
