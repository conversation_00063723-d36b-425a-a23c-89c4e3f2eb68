import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import tailwindcss from "@tailwindcss/vite";
import path from "path";

import Components from "unplugin-vue-components/vite";
import { AntDesignVueResolver } from "unplugin-vue-components/resolvers";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false, // css in js
        }),
      ],
    }),
  ],
  base: process.env.NODE_ENV === "production" ? "./" : "/",
  build: {
    target: "es2015", // 兼容更低版本
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  server: {
    proxy: {
      // 代理 API 请求
      "/basic-api": {
        target: "https://tms2dev.xcspzg.com/basic-api", // 后端服务器地址
        changeOrigin: true, // 支持跨域
        secure: true, // 如果是https接口，需要配置这个参数
        rewrite: (path) => path.replace(/^\/basic-api/, ""), // 重写路径，去掉 /api 前缀
      },
    },
  },
});
